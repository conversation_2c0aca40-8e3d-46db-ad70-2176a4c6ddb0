[build-system]
requires = ["setuptools>=42", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "deveval-adapter"
version = "0.1.0"
description = "T-Bench adapter for DevEval tasks"
readme = "README.md"
requires-python = ">=3.8"
license = { text = "Apache-2.0" }
dependencies = [
    # No external dependencies - uses Python standard library only
]

[tool.black]
line-length = 88
target-version = ["py38", "py39", "py310"] 