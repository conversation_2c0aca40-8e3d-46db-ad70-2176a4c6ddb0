[project]
name = "submodule"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
  # Pull the latest main branch as a zip archive to avoid Git‑LFS checkout
  "mlebench @ https://github.com/openai/mle-bench/archive/refs/heads/main.zip"
]

[build-system]
requires = ["hatchling>=1,<2"]
build-backend = "hatchling.build"

[tool.uv]
# Don’t build & install this project itself—only its dependencies.
package = false
