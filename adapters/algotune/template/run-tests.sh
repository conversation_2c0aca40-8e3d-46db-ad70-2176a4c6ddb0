#!/bin/bash

# This script is designed to first test a candidate "best" solver for performance,
# record its speedup, and then run a final validation test using the original solver.

# Print the solver's code for reference.
echo "--- Final Solver (used for performance test) ---"
cat /app/solver.py
echo "--- End Solver ---"

# --- Step 1: Backup the original solver ---
# Copy the existing solver.py to a backup location before overwriting it.
# This preserves the original code for the final test run.
echo "Backing up original solver..."
cp /app/solver.py /app/solver.py.bak

# --- Step 2: Write the new "best" solver ---
# The new solver code is written to /app/solver.py. This file will be
# used for the performance evaluation in the next step.
{solution_code_command}

# --- Step 3: Run performance test on the new solver ---
# Execute pytest and parse the output to find the "Calculated Speedup".
# The result is saved to a file. If the test fails or the speedup
# cannot be determined, it defaults to "1.0".
echo "Running performance test..."
(set -o pipefail; pytest -s /app/test_outputs.py 2>&1 | tee /app/test_outputs.log | tee /dev/stderr | grep "Calculated Speedup" | awk '{print $3}' | sed 's/x$//' > /app/.best_solver_speedup.txt) \
|| echo "1.0" > /app/.best_solver_speedup.txt

# --- Output Speedup and Solver Code for Review ---
echo "--- TARGET SPEEDUP ---"
cat /app/.best_solver_speedup.txt
echo "--- TARGET SPEED END ---"

# --- Step 4: Restore the original solver ---
# Move the backup file back to /app/solver.py, overwriting the
# temporary "best" solver.
echo "Restoring original solver..."
rm -rf /app/solver.py
mv /app/solver.py.bak /app/solver.py

# --- Step 5: Run final tests on the original solver ---
# Now that the original solver is restored, run the final pytest
# validation to ensure correctness.
echo "Running final validation on original solver..."
pytest $TEST_DIR/test_outputs.py -rA -s

# --- Step 6: Exit ---
# The script has completed its execution.
echo "Script finished."
